import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Share2, UserPlus, Crown, AlertCircle, Clock, Edit2, Check, X } from "lucide-react";
import { DocumentSharingModal } from "./DocumentSharingModal";
import { DocumentAccessIndicator } from "./DocumentAccessIndicator";
import { PermissionRequestModal } from "./PermissionRequestModal";
import { PendingRequestsModal } from "./PendingRequestsModal";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { useToast } from "../hooks/use-toast";

interface DocumentHeaderProps {
  documentId: Id<"documents">;
}

export function DocumentHeader({ documentId }: DocumentHeaderProps) {
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const document = useQuery(api.documents.getDocument, { id: documentId });
  const userRequest = useQuery(api.sharing.getUserPermissionRequest, { documentId });
  const pendingRequests = useQuery(
    api.sharing.getPendingPermissionRequests, 
    permission?.canShare ? { documentId } : "skip"
  );
  
  const [showSharingModal, setShowSharingModal] = useState(false);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [showPendingModal, setShowPendingModal] = useState(false);

  // State for title editing
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editedTitle, setEditedTitle] = useState("");
  const [titleError, setTitleError] = useState("");

  const requestPermissionUpgrade = useMutation(api.sharing.requestPermissionUpgrade);
  const updateDocument = useMutation(api.documents.updateDocument);
  const { toast } = useToast();

  // Validate document title
  const validateTitle = (title: string): string => {
    if (!title.trim()) {
      return "Document name cannot be empty";
    }
    if (title.trim().length > 100) {
      return "Document name must be 100 characters or less";
    }
    // Check for invalid characters (basic validation)
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(title)) {
      return "Document name contains invalid characters";
    }
    return "";
  };

  // Start editing title
  const handleStartEditTitle = () => {
    if (permission?.permission !== "owner" && permission?.permission !== "write") {
      return; // Only owners and write users can edit title
    }
    setEditedTitle(document?.title || "");
    setTitleError("");
    setIsEditingTitle(true);
  };

  // Save title changes
  const handleSaveTitle = async () => {
    const error = validateTitle(editedTitle);
    if (error) {
      setTitleError(error);
      return;
    }

    try {
      await updateDocument({
        id: documentId,
        title: editedTitle.trim(),
      });

      setIsEditingTitle(false);
      setTitleError("");

      toast({
        title: "Document renamed",
        description: `Document renamed to "${editedTitle.trim()}"`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to rename document",
        variant: "destructive",
      });
    }
  };

  // Cancel title editing
  const handleCancelEditTitle = () => {
    setIsEditingTitle(false);
    setEditedTitle("");
    setTitleError("");
  };

  const handleRequestUpgrade = async (message?: string) => {
    try {
      await requestPermissionUpgrade({ documentId, message });
      toast({
        title: "Request sent",
        description: "Permission request sent to document owner!",
      });
      setShowRequestModal(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send request",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isEditingTitle ? (
                <div className="flex items-center gap-2">
                  <div className="space-y-1">
                    <Input
                      value={editedTitle}
                      onChange={(e) => {
                        setEditedTitle(e.target.value);
                        if (titleError) setTitleError("");
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !titleError && editedTitle.trim()) {
                          handleSaveTitle();
                        } else if (e.key === "Escape") {
                          handleCancelEditTitle();
                        }
                      }}
                      className={`text-2xl font-semibold h-auto py-1 px-2 ${titleError ? "border-red-500" : ""}`}
                      autoFocus
                    />
                    {titleError && (
                      <p className="text-sm text-red-600">{titleError}</p>
                    )}
                  </div>
                  <Button
                    size="sm"
                    onClick={handleSaveTitle}
                    disabled={!editedTitle.trim() || !!titleError}
                    className="h-8 w-8 p-0"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEditTitle}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2 group">
                  <h1 className="text-2xl font-semibold text-foreground">
                    {document?.title || "Untitled Document"}
                  </h1>
                  {(permission?.permission === "owner" || permission?.permission === "write") && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleStartEditTitle}
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              )}
              <DocumentAccessIndicator documentId={documentId} />
              
              {/* Owner Information */}
              {permission?.owner && permission.permission !== "owner" && (
                <Badge variant="secondary" className="gap-1">
                  <Crown className="h-3 w-3" />
                  Owned by {permission.owner.name || permission.owner.email}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Request Edit Access Button */}
              {permission?.permission === "read" && !userRequest && (
                <Button
                  onClick={() => setShowRequestModal(true)}
                  className="gap-2"
                  variant="default"
                >
                  <UserPlus className="h-4 w-4" />
                  Request Edit Access
                </Button>
              )}

              {/* Pending Request Status */}
              {userRequest && (
                <Badge variant="secondary" className="gap-1">
                  <Clock className="h-3 w-3" />
                  Request Pending
                </Badge>
              )}

              {/* Pending Requests Notification for Owner */}
              {permission?.canShare && pendingRequests && pendingRequests.length > 0 && (
                <Button
                  onClick={() => setShowPendingModal(true)}
                  variant="destructive"
                  className="gap-2 relative"
                >
                  <AlertCircle className="h-4 w-4" />
                  {pendingRequests.length} Request{pendingRequests.length !== 1 ? 's' : ''}
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </Button>
              )}

              {/* Share Button */}
              {permission?.canShare && (
                <Button
                  onClick={() => setShowSharingModal(true)}
                  className="gap-2"
                >
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <DocumentSharingModal
        documentId={documentId}
        isOpen={showSharingModal}
        onClose={() => setShowSharingModal(false)}
      />

      <PermissionRequestModal
        isOpen={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        onSubmit={handleRequestUpgrade}
        ownerName={permission?.owner?.name || permission?.owner?.email || "Document Owner"}
      />

      <PendingRequestsModal
        documentId={documentId}
        isOpen={showPendingModal}
        onClose={() => setShowPendingModal(false)}
      />
    </>
  );
}
