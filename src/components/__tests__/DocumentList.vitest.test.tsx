/**
 * Frontend Component Tests for DocumentList using Vitest
 * Tests DocumentList component rendering for each permission level
 * Verifies correct UI elements are shown/hidden based on user permissions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useQuery, useMutation } from 'convex/react';
import { DocumentList } from '../DocumentList';
import { useToast } from '../../hooks/use-toast';

// Mock the hooks
vi.mock('convex/react');
vi.mock('../../hooks/use-toast');

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseToast = vi.mocked(useToast);

describe('DocumentList Component', () => {
  const mockOnSelectDocument = vi.fn();
  const mockOnDocumentCountChange = vi.fn();
  const mockCreateDocument = vi.fn();
  const mockDeleteDocument = vi.fn();
  const mockToast = vi.fn();

  // Mock query results
  let mockDocumentsResult: any = [];
  let mockCanCreateResult: any = { canCreate: true, reason: 'authenticated_user' };

  const defaultProps = {
    onSelectDocument: mockOnSelectDocument,
    selectedDocumentId: undefined,
    onDocumentCountChange: mockOnDocumentCountChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Reset mock results to default values
    mockDocumentsResult = [];
    mockCanCreateResult = { canCreate: true, reason: 'authenticated_user' };

    // Set up useQuery mock to return our controlled values
    mockUseQuery.mockImplementation((query) => {
      // Use a simple counter to differentiate calls
      const callCount = mockUseQuery.mock.calls.length;
      if (callCount === 1) {
        return mockDocumentsResult;
      } else if (callCount === 2) {
        return mockCanCreateResult;
      }
      return undefined;
    });

    // Set up useMutation mock
    mockUseMutation.mockImplementation((mutation) => {
      const callCount = mockUseMutation.mock.calls.length;
      if (callCount === 1) {
        return mockCreateDocument;
      } else if (callCount === 2) {
        return mockDeleteDocument;
      }
      return vi.fn();
    });

    mockUseToast.mockReturnValue({ toast: mockToast });
  });

  describe('Loading State', () => {
    it('should show loading spinner when documents are loading', () => {
      mockDocumentsResult = undefined;
      mockCanCreateResult = { canCreate: true };

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Loading documents...')).toBeInTheDocument();
      expect(screen.getByText('Loading documents...').previousElementSibling).toHaveClass('animate-spin'); // Loading spinner
    });
  });

  describe('Permission-Based UI Rendering', () => {
    const mockDocuments = [
      {
        _id: 'doc1',
        title: 'My Document',
        permission: 'owner',
        _creationTime: Date.now(),
        createdBy: 'user1',
        owner: { name: 'John Doe', email: '<EMAIL>' }
      },
      {
        _id: 'doc2',
        title: 'Shared Document',
        permission: 'read',
        _creationTime: Date.now() - 1000,
        createdBy: 'user2',
        owner: { name: 'Jane Smith', email: '<EMAIL>' }
      }
    ];

    it('should show create button for users with create permissions', () => {
      mockDocumentsResult = mockDocuments;
      mockCanCreateResult = { canCreate: true, reason: 'authenticated_user' };

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
      expect(screen.queryByText(/limited access/i)).not.toBeInTheDocument();
    });

    it('should show warning for anonymous users', () => {
      mockUseQuery.mockImplementation((query) => {
        const callCount = mockUseQuery.mock.calls.length;
        if (callCount === 1) return [];
        if (callCount === 2) return { canCreate: false, reason: 'anonymous_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.queryByRole('button', { name: /new document/i })).not.toBeInTheDocument();
      expect(screen.getByText(/limited access/i)).toBeInTheDocument();
      expect(screen.getByText(/anonymous users can only view shared documents/i)).toBeInTheDocument();
    });
  });

  describe('Empty States', () => {
    it('should show appropriate empty state for users who can create documents', () => {
      mockUseQuery.mockImplementation((query) => {
        const callCount = mockUseQuery.mock.calls.length;
        if (callCount === 1) return [];
        if (callCount === 2) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('No documents yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first document to get started.')).toBeInTheDocument();
    });

    it('should show appropriate empty state for anonymous users', () => {
      mockUseQuery.mockImplementation((query) => {
        const callCount = mockUseQuery.mock.calls.length;
        if (callCount === 1) return [];
        if (callCount === 2) return { canCreate: false, reason: 'anonymous_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Sign up for an account or ask someone to share a document with you.')).toBeInTheDocument();
    });
  });

  describe('Document Creation (Seamless)', () => {
    beforeEach(() => {
      // Clear previous mock calls to reset the counter
      vi.clearAllMocks();

      // Reset mock results for this test suite
      mockDocumentsResult = [];
      mockCanCreateResult = { canCreate: true, reason: 'authenticated_user' };

      // Use persistent mocks that don't get consumed
      mockUseQuery.mockImplementation((query) => {
        const callCount = mockUseQuery.mock.calls.length;
        if (callCount % 2 === 1) return mockDocumentsResult; // Odd calls: documents query
        if (callCount % 2 === 0) return mockCanCreateResult; // Even calls: canCreate query
        return undefined;
      });

      mockUseMutation.mockImplementation((mutation) => {
        const callCount = mockUseMutation.mock.calls.length;
        if (callCount % 2 === 1) return mockCreateDocument; // Odd calls: createDocument mutation
        if (callCount % 2 === 0) return mockDeleteDocument; // Even calls: deleteDocument mutation
        return vi.fn();
      });

      mockUseToast.mockReturnValue({ toast: mockToast });
    });

    it('should create document seamlessly when new document button is clicked', async () => {
      mockCreateDocument.mockResolvedValue('new-doc-id');

      const user = userEvent.setup();
      render(<DocumentList {...defaultProps} />);

      const createButton = screen.getByRole('button', { name: /new document/i });
      await user.click(createButton);

      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({
          title: 'Untitled Document'
        });
      });

      expect(mockOnSelectDocument).toHaveBeenCalledWith('new-doc-id');
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Document created',
        description: 'Your new document is ready to edit!',
      });
    });

    it('should show creating state while document is being created', async () => {
      let resolveCreate: (value: string) => void;
      const createPromise = new Promise<string>((resolve) => {
        resolveCreate = resolve;
      });
      mockCreateDocument.mockReturnValue(createPromise);

      const user = userEvent.setup();
      render(<DocumentList {...defaultProps} />);

      const createButton = screen.getByRole('button', { name: /new document/i });
      await user.click(createButton);

      // Should show creating state
      expect(screen.getByRole('button', { name: /creating.../i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /creating.../i })).toBeDisabled();

      // Resolve the creation
      resolveCreate!('new-doc-id');

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /new document/i })).not.toBeDisabled();
      });
    });

    it('should handle creation errors gracefully', async () => {
      const errorMessage = 'Failed to create document';
      mockCreateDocument.mockRejectedValue(new Error(errorMessage));

      const user = userEvent.setup();
      render(<DocumentList {...defaultProps} />);

      const createButton = screen.getByRole('button', { name: /new document/i });
      await user.click(createButton);

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      });

      expect(mockOnSelectDocument).not.toHaveBeenCalled();
    });

    it('should show appropriate message for users with create permissions', () => {
      mockDocumentsResult = [];
      mockCanCreateResult = { canCreate: true, reason: 'authenticated_user' };

      render(<DocumentList {...defaultProps} />);

      // Should show empty state for users who can create
      expect(screen.getByText('No documents yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first document to get started.')).toBeInTheDocument();
    });
  });
});
